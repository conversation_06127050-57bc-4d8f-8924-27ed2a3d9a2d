class PersianUtils {
  // Convert English numbers to Persian numbers
  static String toPersianNumbers(String input) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    
    String result = input;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], persianNumbers[i]);
    }
    return result;
  }

  // Convert Persian numbers to English numbers
  static String toEnglishNumbers(String input) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    
    String result = input;
    for (int i = 0; i < persianNumbers.length; i++) {
      result = result.replaceAll(persianNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  // Format numbers with Persian digits
  static String formatPersianNumber(int number) {
    return toPersianNumbers(number.toString());
  }

  // Format time with Persian digits
  static String formatPersianTime(String time) {
    return toPersianNumbers(time);
  }
}
