import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  Future<bool> load() async {
    String jsonString = await rootBundle.loadString('lib/l10n/app_fa.arb');
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    _localizedStrings = jsonMap.map((key, value) {
      return MapEntry(key, value.toString());
    });

    return true;
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // App Identity
  String get appTitle => translate('appTitle');

  // Onboarding
  String get welcomeTitle => translate('welcomeTitle');
  String get welcomeDescription => translate('welcomeDescription');
  String get breathingTechniquesTitle => translate('breathingTechniquesTitle');
  String get breathingTechniquesDescription =>
      translate('breathingTechniquesDescription');
  String get progressTrackingTitle => translate('progressTrackingTitle');
  String get progressTrackingDescription =>
      translate('progressTrackingDescription');
  String get personalizationTitle => translate('personalizationTitle');
  String get personalizationDescription =>
      translate('personalizationDescription');
  String get enableReminders => translate('enableReminders');
  String get maybeLater => translate('maybeLater');
  String get reminderDescription => translate('reminderDescription');

  // Home Screen
  String welcomeUser(String userTitle) =>
      translate('welcomeUser').replaceAll('{userTitle}', userTitle);
  String get whatToPracticeToday => translate('whatToPracticeToday');

  // Breathing Techniques
  String get bellyBreathing => translate('bellyBreathing');
  String get bellyBreathingDescription =>
      translate('bellyBreathingDescription');
  String get pursedLipBreathing => translate('pursedLipBreathing');
  String get pursedLipBreathingDescription =>
      translate('pursedLipBreathingDescription');
  String get fourSevenEightBreathing => translate('fourSevenEightBreathing');
  String get fourSevenEightBreathingDescription =>
      translate('fourSevenEightBreathingDescription');
  String get boxBreathing => translate('boxBreathing');
  String get boxBreathingDescription => translate('boxBreathingDescription');
  String get alternateNostrilBreathing =>
      translate('alternateNostrilBreathing');
  String get alternateNostrilBreathingDescription =>
      translate('alternateNostrilBreathingDescription');
  String get bhramariPranayama => translate('bhramariPranayama');
  String get bhramariPranayamaDescription =>
      translate('bhramariPranayamaDescription');
  String get kapalBhatiPranayama => translate('kapalBhatiPranayama');
  String get kapalBhatiPranayamaDescription =>
      translate('kapalBhatiPranayamaDescription');

  // Benefits
  String get reducesStress => translate('reducesStress');
  String get lowersHeartRate => translate('lowersHeartRate');
  String get improvesFocus => translate('improvesFocus');
  String get improvesBreathingEfficiency =>
      translate('improvesBreathingEfficiency');
  String get reducesShortness => translate('reducesShortness');
  String get calmsMind => translate('calmsMind');
  String get promotesRelaxation => translate('promotesRelaxation');
  String get reducesAnxiety => translate('reducesAnxiety');
  String get aidsSleep => translate('aidsSleep');
  String get balancesEmotions => translate('balancesEmotions');
  String get balancesNervousSystem => translate('balancesNervousSystem');
  String get enhancesMentalClarity => translate('enhancesMentalClarity');
  String get relievesTension => translate('relievesTension');
  String get reducesAnger => translate('reducesAnger');
  String get improvesSleepQuality => translate('improvesSleepQuality');
  String get boostsEnergy => translate('boostsEnergy');
  String get improvesDigestion => translate('improvesDigestion');
  String get enhancesMentalAlertness => translate('enhancesMentalAlertness');

  // User Titles
  String get breathNovice => translate('breathNovice');
  String get calmSeeker => translate('calmSeeker');
  String get breathMaster => translate('breathMaster');
  String get breathLegend => translate('breathLegend');

  // Challenges
  String get sevenDayStreak => translate('sevenDayStreak');
  String get thirtyDayStreak => translate('thirtyDayStreak');
  String get exerciseExplorer => translate('exerciseExplorer');
  String get tenExercisesCompleted => translate('tenExercisesCompleted');
  String get fiftyExercisesCompleted => translate('fiftyExercisesCompleted');
  String get levelTwoAchieved => translate('levelTwoAchieved');
  String get dailyDeepBreathing => translate('dailyDeepBreathing');
  String get weeklyRelaxation => translate('weeklyRelaxation');

  // Challenge Descriptions
  String get completeSevenDays => translate('completeSevenDays');
  String get completeThirtyDays => translate('completeThirtyDays');
  String get tryAllTechniques => translate('tryAllTechniques');
  String get completeTenExercises => translate('completeTenExercises');
  String get completeFiftyExercises => translate('completeFiftyExercises');
  String get reachLevelTwo => translate('reachLevelTwo');
  String get completeChallenge => translate('completeChallenge');

  // UI Elements
  String get startBreathingExercise => translate('startBreathingExercise');
  String get allChallengesCompleted => translate('allChallengesCompleted');
  String get days => translate('days');
  String get techniquesTried => translate('techniquesTried');
  String get exercises => translate('exercises');
  String get level => translate('level');
  String get styles => translate('styles');

  // Mood Dialog
  String get howDoYouFeelNow => translate('howDoYouFeelNow');
  String get happy => translate('happy');
  String get relaxed => translate('relaxed');
  String get neutral => translate('neutral');
  String get tired => translate('tired');
  String get angry => translate('angry');
  String get skip => translate('skip');

  // Settings
  String get notifications => translate('notifications');
  String get enableDisableReminders => translate('enableDisableReminders');
  String get reminderTime => translate('reminderTime');
  String get setDailyReminderTime => translate('setDailyReminderTime');
  String get hapticFeedback => translate('hapticFeedback');
  String get vibrationCues => translate('vibrationCues');
  String get backgroundMusic => translate('backgroundMusic');
  String get playRelaxingMusic => translate('playRelaxingMusic');

  // Breathing Screen
  String get wellDone => translate('wellDone');
  String get getReady => translate('getReady');
  String cycle(int current, int total) => translate('cycle')
      .replaceAll('{current}', current.toString())
      .replaceAll('{total}', total.toString());
  String get play => translate('play');
  String get pause => translate('pause');

  // Error Messages
  String get musicLoadingFailed => translate('musicLoadingFailed');
  String get internetConnectionLost => translate('internetConnectionLost');
  String get playbackError => translate('playbackError');
  String get retry => translate('retry');

  // Unlock Dialog
  String get newTechniqueUnlocked => translate('newTechniqueUnlocked');
  String get newTechniquesUnlocked => translate('newTechniquesUnlocked');
  String get keepPracticing => translate('keepPracticing');
  String get awesome => translate('awesome');
  String unlockAt(String level) =>
      translate('unlockAt').replaceAll('{level}', level);

  // Notification Settings
  String get tapSwitchToRequest => translate('tapSwitchToRequest');
  String get permissionDenied => translate('permissionDenied');
  String get enableInPhoneSettings => translate('enableInPhoneSettings');

  // Navigation
  String get points => translate('points');
  String get moodAnalysis => translate('moodAnalysis');
  String get profile => translate('profile');
  String get challenges => translate('challenges');
  String get home => translate('home');
  String get settings => translate('settings');

  // Breathing Instructions
  String get inhale => translate('inhale');
  String get exhale => translate('exhale');
  String get hold => translate('hold');
  String get sitComfortably => translate('sitComfortably');
  String get placeHands => translate('placeHands');
  String get inhaleDeepFour => translate('inhaleDeepFour');
  String get exhaleSlowSix => translate('exhaleSlowSix');

  // Notification Messages
  String get breathlyReminderTitle => translate('breathlyReminderTitle');
  String get breathlyReminderBody => translate('breathlyReminderBody');

  // Technique Lock Messages
  String techniqueIsLocked(String techniqueName) =>
      translate('techniqueIsLocked')
          .replaceAll('{techniqueName}', techniqueName);
  String needToReachLevel(String requiredTitle) => translate('needToReachLevel')
      .replaceAll('{requiredTitle}', requiredTitle);
  String currentLevel(String currentTitle) =>
      translate('currentLevel').replaceAll('{currentTitle}', currentTitle);
  String needMorePoints(int pointsNeeded) => translate('needMorePoints')
      .replaceAll('{pointsNeeded}', pointsNeeded.toString());
  String get completeExercisesToEarnPoints =>
      translate('completeExercisesToEarnPoints');

  // Profile Stats
  String get dayStreak => translate('dayStreak');
  String get levelProgress => translate('levelProgress');
  String get inProgress => translate('inProgress');

  // Requirements
  String earnMorePoints(int points) =>
      translate('earnMorePoints').replaceAll('{points}', points.toString());
  String completeMoreChallenges(int challenges) =>
      translate('completeMoreChallenges')
          .replaceAll('{challenges}', challenges.toString());
  String earnPointsAndCompleteChallenges(int points, int challenges) =>
      translate('earnPointsAndCompleteChallenges')
          .replaceAll('{points}', points.toString())
          .replaceAll('{challenges}', challenges.toString());

  // Exercise History
  String youCompletedTechnique(String technique, String date) =>
      translate('youCompletedTechnique')
          .replaceAll('{technique}', technique)
          .replaceAll('{date}', date);

  // Recent Activity
  String get recentActivity => translate('recentActivity');
  String get seeAll => translate('seeAll');
  String get errorLoadingRecentActivity =>
      translate('errorLoadingRecentActivity');
  String get noBreathingExercisesCompleted =>
      translate('noBreathingExercisesCompleted');
  String get completeExerciseToSeeHere =>
      translate('completeExerciseToSeeHere');
  String get ok => translate('ok');

  // Breathing Screen Additional
  String get completed => translate('completed');
  String get startingSoon => translate('startingSoon');
  String get practiceAgain => translate('practiceAgain');
  String get start => translate('start');
  String get resume => translate('resume');
  String get seconds => translate('seconds');

  // Music Player
  String get localFileAccessError => translate('localFileAccessError');
  String get selectMusicTrack => translate('selectMusicTrack');
  String get notDownloaded => translate('notDownloaded');
  String get selectMusic => translate('selectMusic');
  String get tapToChangeMusic => translate('tapToChangeMusic');

  // Profile Screen
  String congratulationsLevel(int level) =>
      translate('congratulationsLevel').replaceAll('{level}', level.toString());
  String congratulationsLevelWithImage(int level, String title) =>
      translate('congratulationsLevelWithImage')
          .replaceAll('{level}', level.toString())
          .replaceAll('{title}', title);
  String get yourMoodImprovingRecently =>
      translate('yourMoodImprovingRecently');
  String get yourMoodDecliningRecently =>
      translate('yourMoodDecliningRecently');
  String get yourMoodRelativelyStable => translate('yourMoodRelativelyStable');
  String get noChallengesCompletedYet => translate('noChallengesCompletedYet');
  String get completeExercisesConsistently =>
      translate('completeExercisesConsistently');
  String get youveReachedHighestTitle => translate('youveReachedHighestTitle');

  // Settings Screen
  String get musicWillPlayDuringExercises =>
      translate('musicWillPlayDuringExercises');
  String get close => translate('close');
  String get availableOffline => translate('availableOffline');
  String get streamsFromInternet => translate('streamsFromInternet');
  String get downloadForOfflineUse => translate('downloadForOfflineUse');
  String get notificationPermission => translate('notificationPermission');
  String get notificationPermissionRequired =>
      translate('notificationPermissionRequired');
  String get remindersNotExact => translate('remindersNotExact');
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['fa'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
