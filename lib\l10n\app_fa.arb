{"@@locale": "fa", "appTitle": "تن‌آرام", "@appTitle": {"description": "The title of the application"}, "welcomeTitle": "به تن‌آرام خوش آمدید!", "@welcomeTitle": {"description": "Welcome title on onboarding page 1"}, "welcomeDescription": "قدرت نفس خود را کشف کنید. استرس را کاهش دهید، تمرکز را بهبود بخشید و سلامتی خود را با تمرینات تنفسی هدایت شده ارتقا دهید.", "@welcomeDescription": {"description": "Welcome description on onboarding page 1"}, "breathingTechniquesTitle": "تکنیک‌های تنفسی", "@breathingTechniquesTitle": {"description": "Title for breathing techniques section"}, "breathingTechniquesDescription": "از تکنیک‌های مختلف تنفسی برای آرامش، تمرکز و خواب بهتر استفاده کنید.", "@breathingTechniquesDescription": {"description": "Description for breathing techniques on onboarding page 2"}, "progressTrackingTitle": "رشد کنید و پیشرفت خود را ببینید", "@progressTrackingTitle": {"description": "Title for progress tracking on onboarding page 3"}, "progressTrackingDescription": "امتیاز کسب کنید، سطح خود را ارتقا دهید و چالش‌ها را تکمیل کنید. حال و هوای خود را پس از هر جلسه ردیابی کنید و ببینید که سلامتی شما در طول زمان با نمودارهای بصری چگونه بهبود می‌یابد.", "@progressTrackingDescription": {"description": "Description for progress tracking on onboarding page 3"}, "personalizationTitle": "شخصی‌سازی تجربه شما", "@personalizationTitle": {"description": "Title for personalization on onboarding page 4"}, "personalizationDescription": "تمرین خود را با موسیقی پس‌زمینه شخصی‌سازی کنید، یادآوری‌های روزانه تنظیم کنید تا عادت ثابتی ایجاد کنید و با دستیابی به سطوح جدید، آواتارهای جدید پروفایل را باز کنید.", "@personalizationDescription": {"description": "Description for personalization on onboarding page 4"}, "enableReminders": "فعال‌سازی یادآوری‌ها", "@enableReminders": {"description": "Button text to enable reminders"}, "maybeLater": "شاید بعداً", "@maybeLater": {"description": "Button text for maybe later"}, "reminderDescription": "یادآوری‌های ملایم برای تمرین تنفس و ایجاد روتین سلامتی ثابت دریافت کنید.", "@reminderDescription": {"description": "Description for reminder feature"}, "welcomeUser": "خو<PERSON> آمدید، {userTitle}", "@welcomeUser": {"description": "Welcome message with user title", "placeholders": {"userTitle": {"type": "String", "example": "تازه‌کار تنفس"}}}, "whatToPracticeToday": "امروز چه چیزی را می‌خواهید تمرین کنید؟", "@whatToPracticeToday": {"description": "Question asking what to practice today"}, "bellyBreathing": "تنفس شکمی", "@bellyBreathing": {"description": "Belly breathing technique name"}, "bellyBreathingDescription": "تکنیک ساده‌ای که بر تنفس عمیق در دیافراگم متمرکز است تا آرامش و جریان اکسیژن را تقویت کند.", "@bellyBreathingDescription": {"description": "Belly breathing technique description"}, "pursedLipBreathing": "تنفس با لب‌های جمع شده", "@pursedLipBreathing": {"description": "Pursed lip breathing technique name"}, "pursedLipBreathingDescription": "تکنیکی برای کند کردن تنفس و بهبود تبادل اکسیژن که اغلب برای بیماری‌های ریوی استفاده می‌شود.", "@pursedLipBreathingDescription": {"description": "Pursed lip breathing technique description"}, "fourSevenEightBreathing": "تنفس ۴-۷-۸", "@fourSevenEightBreathing": {"description": "4-7-8 breathing technique name"}, "fourSevenEightBreathingDescription": "تکنیک آرام‌بخشی که برای کاهش اضطراب و کمک به خواب طراحی شده است.", "@fourSevenEightBreathingDescription": {"description": "4-7-8 breathing technique description"}, "boxBreathing": "تنفس جعبه‌ای", "@boxBreathing": {"description": "Box breathing technique name"}, "boxBreathingDescription": "تکنیک ساختاریافته‌ای که توسط ورزشکاران و متخصصان برای تقویت تمرکز و آرام کردن ذهن استفاده می‌شود.", "@boxBreathingDescription": {"description": "Box breathing technique description"}, "alternateNostrilBreathing": "تنفس متناوب سوراخ بینی", "@alternateNostrilBreathing": {"description": "Alternate nostril breathing technique name"}, "alternateNostrilBreathingDescription": "تکنیک یوگا که سیستم عصبی را متعادل می‌کند و وضوح ذهنی را افزایش می‌دهد.", "@alternateNostrilBreathingDescription": {"description": "Alternate nostril breathing technique description"}, "bhramariPranayama": "پرانایاما بهرامری (زنبور)", "@bhramariPranayama": {"description": "<PERSON><PERSON><PERSON><PERSON> technique name"}, "bhramariPranayamaDescription": "تکنیک تنفس زمزمه‌ای که سیستم عصبی را با ارتعاش صدا آرام می‌کند.", "@bhramariPranayamaDescription": {"description": "<PERSON><PERSON><PERSON><PERSON> technique description"}, "kapalBhatiPranayama": "پرانایاما کاپال بهاتی", "@kapalBhatiPranayama": {"description": "<PERSON><PERSON> technique name"}, "kapalBhatiPranayamaDescription": "تکنیک انرژی‌بخش شامل بازدم‌های قوی برای پاکسازی بدن و ذهن.", "@kapalBhatiPranayamaDescription": {"description": "<PERSON><PERSON> technique description"}, "reducesStress": "کاهش استرس", "@reducesStress": {"description": "Benefit: reduces stress"}, "lowersHeartRate": "کاهش ضربان قلب", "@lowersHeartRate": {"description": "Benefit: lowers heart rate"}, "improvesFocus": "به<PERSON>و<PERSON> تمرکز", "@improvesFocus": {"description": "Benefit: improves focus"}, "improvesBreathingEfficiency": "بهبود کارایی تنفس", "@improvesBreathingEfficiency": {"description": "Benefit: improves breathing efficiency"}, "reducesShortness": "کاهش تنگی نفس", "@reducesShortness": {"description": "Benefit: reduces shortness of breath"}, "calmsMind": "آرام کردن ذهن", "@calmsMind": {"description": "Benefit: calms the mind"}, "promotesRelaxation": "تقویت آرامش", "@promotesRelaxation": {"description": "Benefit: promotes relaxation"}, "reducesAnxiety": "کاهش اضطراب", "@reducesAnxiety": {"description": "Benefit: reduces anxiety"}, "aidsSleep": "کمک به خواب", "@aidsSleep": {"description": "Benefit: aids sleep"}, "balancesEmotions": "متعادل کردن احساسات", "@balancesEmotions": {"description": "Benefit: balances emotions"}, "balancesNervousSystem": "متعادل کردن سیستم عصبی", "@balancesNervousSystem": {"description": "Benefit: balances nervous system"}, "enhancesMentalClarity": "تقویت وضوح ذهنی", "@enhancesMentalClarity": {"description": "Benefit: enhances mental clarity"}, "relievesTension": "کاهش تنش", "@relievesTension": {"description": "Benefit: relieves tension"}, "reducesAnger": "کاهش خشم", "@reducesAnger": {"description": "Benefit: reduces anger"}, "improvesSleepQuality": "<PERSON><PERSON><PERSON><PERSON><PERSON> کیفیت خواب", "@improvesSleepQuality": {"description": "Benefit: improves sleep quality"}, "boostsEnergy": "افزایش انرژی", "@boostsEnergy": {"description": "Benefit: boosts energy"}, "improvesDigestion": "به<PERSON>و<PERSON> هضم", "@improvesDigestion": {"description": "Benefit: improves digestion"}, "enhancesMentalAlertness": "تقویت هوشیاری ذهنی", "@enhancesMentalAlertness": {"description": "Benefit: enhances mental alertness"}, "breathNovice": "تازه‌کار تنفس", "@breathNovice": {"description": "User title: <PERSON><PERSON>"}, "calmSeeker": "جویای آرامش", "@calmSeeker": {"description": "Calm Seeker title (duplicate but needed for consistency)"}, "breathMaster": "استاد تنفس", "@breathMaster": {"description": "User title: Breath Master"}, "breathLegend": "افسانه تنفس", "@breathLegend": {"description": "User title: Breath Legend"}, "sevenDayStreak": "رکورد ۷ روزه", "@sevenDayStreak": {"description": "7-Day Streak challenge"}, "thirtyDayStreak": "رکورد ۳۰ روزه", "@thirtyDayStreak": {"description": "30-Day Streak challenge"}, "exerciseExplorer": "کاوشگر تمرینات", "@exerciseExplorer": {"description": "Exercise Explorer challenge"}, "tenExercisesCompleted": "۱۰ تمرین تکمیل شده", "@tenExercisesCompleted": {"description": "10 Exercises Completed challenge"}, "fiftyExercisesCompleted": "۵۰ تمرین تکمیل شده", "@fiftyExercisesCompleted": {"description": "50 Exercises Completed challenge"}, "levelTwoAchieved": "دستیابی به سطح ۲", "@levelTwoAchieved": {"description": "Level 2 Achieved challenge"}, "dailyDeepBreathing": "تنفس عمیق روزانه", "@dailyDeepBreathing": {"description": "Daily Deep Breathing challenge"}, "weeklyRelaxation": "آرا<PERSON>ش هفتگی", "@weeklyRelaxation": {"description": "Weekly Relaxation challenge"}, "completeSevenDays": "تمرینات تنفسی را برای ۷ روز متوالی تکمیل کنید", "@completeSevenDays": {"description": "Complete breathing exercises for 7 consecutive days"}, "completeThirtyDays": "تمرینات تنفسی را برای ۳۰ روز متوالی تکمیل کنید", "@completeThirtyDays": {"description": "Complete breathing exercises for 30 consecutive days"}, "tryAllTechniques": "حدا<PERSON><PERSON> یک بار همه تکنیک‌های تنفسی را امتحان کنید", "@tryAllTechniques": {"description": "Try all breathing techniques at least once"}, "completeTenExercises": "۱۰ تمرین تنفسی تکمیل کنید", "@completeTenExercises": {"description": "Complete 10 breathing exercises"}, "completeFiftyExercises": "۵۰ تمرین تنفسی تکمیل کنید", "@completeFiftyExercises": {"description": "Complete 50 breathing exercises"}, "reachLevelTwo": "به سطح ۲ برسید", "@reachLevelTwo": {"description": "Reach level 2"}, "completeChallenge": "این چالش را تکمیل کنید تا جوایز کسب کنید", "@completeChallenge": {"description": "Complete this challenge to earn rewards"}, "startBreathingExercise": "شروع تمرین تنفسی", "@startBreathingExercise": {"description": "Start a Breathing Exercise button"}, "allChallengesCompleted": "همه چالش‌ها تکمیل شده!", "@allChallengesCompleted": {"description": "All challenges completed message"}, "days": "روز", "@days": {"description": "Days label for challenges"}, "techniquesTried": "تکنیک‌های امتحان شده", "@techniquesTried": {"description": "Techniques Tried label"}, "exercises": "تمرینات", "@exercises": {"description": "Exercises label"}, "level": "سطح", "@level": {"description": "Level label"}, "styles": "سبک‌ها", "@styles": {"description": "Styles label"}, "howDoYouFeelNow": "الان چه حالی دارید؟", "@howDoYouFeelNow": {"description": "Mood dialog title asking how user feels"}, "happy": "خوشحال", "@happy": {"description": "Happy mood"}, "relaxed": "آرام", "@relaxed": {"description": "Relaxed mood"}, "neutral": "خن<PERSON>ی", "@neutral": {"description": "Neutral mood"}, "tired": "خسته", "@tired": {"description": "Tired mood"}, "angry": "عص<PERSON><PERSON>ی", "@angry": {"description": "Angry mood"}, "skip": "ر<PERSON> کر<PERSON>ن", "@skip": {"description": "Skip button text"}, "notifications": "اعلان‌ها", "@notifications": {"description": "Notifications setting"}, "enableDisableReminders": "فعال یا غیرفعال کردن یادآوری‌های تنفس", "@enableDisableReminders": {"description": "Enable or disable breathing reminders"}, "reminderTime": "زمان یادآوری", "@reminderTime": {"description": "Reminder Time setting"}, "setDailyReminderTime": "زمان یادآوری روزانه تنفس را تنظیم کنید", "@setDailyReminderTime": {"description": "Set your daily breathing reminder time"}, "hapticFeedback": "با<PERSON><PERSON>و<PERSON>د لمسی", "@hapticFeedback": {"description": "Haptic Feedback setting"}, "vibrationCues": "نشانه‌های ارتعاشی در طول تمرینات تنفسی", "@vibrationCues": {"description": "Vibration cues during breathing exercises"}, "backgroundMusic": "موسیقی پس‌زمینه", "@backgroundMusic": {"description": "Background Music setting"}, "playRelaxingMusic": "پخش موسیقی آرام‌بخش در طول تمرینات", "@playRelaxingMusic": {"description": "Play relaxing music during exercises"}, "wellDone": "آفرین!", "@wellDone": {"description": "Well Done message after completing exercise"}, "getReady": "آماده شوید...", "@getReady": {"description": "Get ready message before starting exercise"}, "cycle": "دور {current} از {total}", "@cycle": {"description": "Cycle counter during exercise", "placeholders": {"current": {"type": "int", "example": "1"}, "total": {"type": "int", "example": "5"}}}, "play": "پخش", "@play": {"description": "Play button tooltip"}, "pause": "توقف", "@pause": {"description": "Pause button tooltip"}, "musicLoadingFailed": "بارگذاری موسیقی ناموفق بود. لطفاً دوباره تلاش کنید.", "@musicLoadingFailed": {"description": "Music loading failed error message"}, "internetConnectionLost": "اتصال اینترنت قطع شد. وقتی دوباره متصل شدید، دکمه تلاش مجدد را بزنید.", "@internetConnectionLost": {"description": "Internet connection lost error message"}, "playbackError": "خطا در پخش. لطفاً دوباره تلاش کنید.", "@playbackError": {"description": "Playback error message"}, "retry": "تلاش مجدد", "@retry": {"description": "Retry button"}, "newTechniqueUnlocked": "تکنیک جدید باز شد!", "@newTechniqueUnlocked": {"description": "New technique unlocked message (singular)"}, "newTechniquesUnlocked": "تکنیک‌های جدید باز شدند!", "@newTechniquesUnlocked": {"description": "New techniques unlocked message (plural)"}, "keepPracticing": "به تمرین ادامه دهید تا تکنیک‌های پیشرفته‌تر را باز کنید!", "@keepPracticing": {"description": "Keep practicing motivational message"}, "awesome": "عالی!", "@awesome": {"description": "Awesome button text"}, "unlockAt": "باز شدن در {level}", "@unlockAt": {"description": "Unlock at level message", "placeholders": {"level": {"type": "String", "example": "سطح ۲"}}}, "tapSwitchToRequest": "برای درخواست مجوز اعلان، کلید را بزنید", "@tapSwitchToRequest": {"description": "Tap the switch to request notification permission"}, "permissionDenied": "مجوز رد شد - برای تلاش مجدد یا بررسی تنظیمات بزنید", "@permissionDenied": {"description": "Permission denied message"}, "enableInPhoneSettings": "برای استفاده از یادآوری‌ها در تنظیمات تلفن فعال کنید", "@enableInPhoneSettings": {"description": "Enable in phone settings message"}, "points": "امت<PERSON><PERSON>ز", "@points": {"description": "Points label"}, "moodAnalysis": "تحلیل حال و هوا", "@moodAnalysis": {"description": "Mood Analysis section title"}, "profile": "پروفایل", "@profile": {"description": "Profile tab title"}, "challenges": "چالش‌ها", "@challenges": {"description": "Challenges tab title"}, "home": "خانه", "@home": {"description": "Home tab title"}, "settings": "تنظیمات", "@settings": {"description": "Settings button text (duplicate but needed for consistency)"}, "inhale": "دم", "@inhale": {"description": "Inhale instruction"}, "exhale": "بازدم", "@exhale": {"description": "Exhale instruction"}, "hold": "نگه دارید", "@hold": {"description": "Hold breath instruction (duplicate but needed for consistency)"}, "sitComfortably": "را<PERSON><PERSON> بنشینید یا دراز بکشید.", "@sitComfortably": {"description": "Sit or lie down comfortably instruction"}, "placeHands": "یک دست را روی سینه و دیگری را روی شکم قرار دهید.", "@placeHands": {"description": "Place hands instruction for belly breathing"}, "inhaleDeepFour": "از بینی عمیق نفس بکشید به مدت ۴ ثانیه، احساس کنید شکمتان بالا می‌آید.", "@inhaleDeepFour": {"description": "Inhale deeply for 4 seconds instruction"}, "exhaleSlowSix": "آهسته از دهان نفس بیرون دهید به مدت ۶ ثانیه، احساس کنید شکمتان پایین می‌آید.", "@exhaleSlowSix": {"description": "Exhale slowly for 6 seconds instruction"}, "breathlyReminderTitle": "یادآوری تن‌آرام", "@breathlyReminderTitle": {"description": "Notification title for breathing reminder"}, "breathlyReminderBody": "لحظه‌ای وقت بگذارید تا نفس بکشید و آرام شوید", "@breathlyReminderBody": {"description": "Notification body for breathing reminder"}, "techniqueIsLocked": "{techniqueName} در حال حاضر قفل است.", "@techniqueIsLocked": {"description": "Message when technique is locked", "placeholders": {"techniqueName": {"type": "String", "example": "تنفس شکمی"}}}, "needToReachLevel": "برای باز کردن این تکنیک باید به سطح {requiredTitle} برسید.", "@needToReachLevel": {"description": "Message about required level to unlock technique", "placeholders": {"requiredTitle": {"type": "String", "example": "جویای آرامش"}}}, "currentLevel": "سطح فعلی شما: {currentTitle}", "@currentLevel": {"description": "Current user level message", "placeholders": {"currentTitle": {"type": "String", "example": "تازه‌کار تنفس"}}}, "needMorePoints": "برای باز کردن این تکنیک به {pointsNeeded} امتیاز بیشتر نیاز دارید.", "@needMorePoints": {"description": "Message about points needed to unlock technique", "placeholders": {"pointsNeeded": {"type": "int", "example": "50"}}}, "completeExercisesToEarnPoints": "تمرینات تنفسی و چالش‌ها را تکمیل کنید تا امتیاز کسب کنید و سطح خود را ارتقا دهید!", "@completeExercisesToEarnPoints": {"description": "Motivational message to complete exercises"}, "dayStreak": "رکورد روزانه", "@dayStreak": {"description": "Day streak label (duplicate but needed for consistency)"}, "levelProgress": "پیشرفت سطح", "@levelProgress": {"description": "Level progress label (duplicate but needed for consistency)"}, "inProgress": "در حال انجام", "@inProgress": {"description": "In progress status"}, "earnMorePoints": "کسب {points} امتیاز بیشتر", "@earnMorePoints": {"description": "Earn more points requirement", "placeholders": {"points": {"type": "int", "example": "50"}}}, "completeMoreChallenges": "تکمیل {challenges} چالش بیشتر", "@completeMoreChallenges": {"description": "Complete more challenges requirement", "placeholders": {"challenges": {"type": "int", "example": "2"}}}, "earnPointsAndCompleteChallenges": "کسب {points} امتیاز بیشتر و تکمیل {challenges} چالش بیشتر", "@earnPointsAndCompleteChallenges": {"description": "Earn points and complete challenges requirement", "placeholders": {"points": {"type": "int", "example": "50"}, "challenges": {"type": "int", "example": "2"}}}, "youCompletedTechnique": "شما {technique} را در تاریخ {date} تکمیل کردید", "@youCompletedTechnique": {"description": "Message showing completed technique", "placeholders": {"technique": {"type": "String", "example": "تنفس شکمی"}, "date": {"type": "String", "example": "۱۴۰۳/۰۱/۰۱"}}}, "recentActivity": "فعالیت‌های اخیر", "@recentActivity": {"description": "Recent Activity section title"}, "seeAll": "مشاهده همه", "@seeAll": {"description": "See All button text"}, "errorLoadingRecentActivity": "خطا در بارگذاری فعالیت‌های اخیر", "@errorLoadingRecentActivity": {"description": "Error message when loading recent activity fails"}, "noBreathingExercisesCompleted": "هنوز هیچ تمرین تنفسی تکمیل نشده است", "@noBreathingExercisesCompleted": {"description": "Message when no exercises have been completed"}, "completeExerciseToSeeHere": "یک تمرین تکمیل کنید تا اینجا نمایش داده شود", "@completeExerciseToSeeHere": {"description": "Message encouraging user to complete an exercise"}, "ok": "باشه", "@ok": {"description": "OK button text"}, "completed": "تکمیل شد!", "@completed": {"description": "Completed message after finishing exercise"}, "startingSoon": "به زودی شروع می‌شود...", "@startingSoon": {"description": "Starting soon message during get ready phase"}, "practiceAgain": "تمرین مجدد", "@practiceAgain": {"description": "Practice again button text"}, "start": "شروع", "@start": {"description": "Start button text"}, "resume": "ادامه", "@resume": {"description": "Resume button text"}, "seconds": "ثانیه", "@seconds": {"description": "Seconds unit"}, "calm": "آرام", "@calm": {"description": "Calm mood"}, "localFileAccessError": "خطا در دسترسی به فایل محلی. دوباره دانلود کنید.", "@localFileAccessError": {"description": "Local file access error message"}, "selectMusicTrack": "انتخاب موسیقی", "@selectMusicTrack": {"description": "Select music track dialog title"}, "notDownloaded": "دان<PERSON>ود نشده", "@notDownloaded": {"description": "Not downloaded status for music tracks"}, "selectMusic": "انتخاب موسیقی:", "@selectMusic": {"description": "Select music label (duplicate but needed for consistency)"}, "tapToChangeMusic": "برای تغییر موسیقی بزنید", "@tapToChangeMusic": {"description": "Tap to change music instruction"}, "congratulationsLevel": "تبریک! شما به سطح {level} رسیدید!", "@congratulationsLevel": {"description": "Congratulations level up message", "placeholders": {"level": {"type": "int", "example": "2"}}}, "congratulationsLevelWithImage": "تبریک! شما به سطح {level} رسیدید! تصویر پروفایل {title} باز شد!", "@congratulationsLevelWithImage": {"description": "Congratulations level up message with profile image unlock", "placeholders": {"level": {"type": "int", "example": "2"}, "title": {"type": "String", "example": "جویای آرامش"}}}, "yourMoodImprovingRecently": "حال و هوای شما اخیراً بهتر شده است!", "@yourMoodImprovingRecently": {"description": "Mood improving trend message"}, "yourMoodDecliningRecently": "حال و هوای شما اخیراً بدتر شده است.", "@yourMoodDecliningRecently": {"description": "Mood declining trend message"}, "yourMoodRelativelyStable": "حال و هوای شما نسبتاً پایدار بوده است.", "@yourMoodRelativelyStable": {"description": "Mood stable trend message"}, "noChallengesCompletedYet": "هنوز هیچ چالشی تکمیل نشده است", "@noChallengesCompletedYet": {"description": "No challenges completed message"}, "completeExercisesConsistently": "تمرینات را به طور مداوم تکمیل کنید تا چالش‌ها کسب کنید", "@completeExercisesConsistently": {"description": "Complete exercises consistently message"}, "youveReachedHighestTitle": "شما به بالاترین عنوان رسیده‌اید!", "@youveReachedHighestTitle": {"description": "Highest title reached message"}, "musicWillPlayDuringExercises": "موسیقی در طول تمرینات تنفسی پخش خواهد شد", "@musicWillPlayDuringExercises": {"description": "Music will play during exercises info"}, "close": "بستن", "@close": {"description": "Close button text"}, "availableOffline": "آفلاین در دسترس", "@availableOffline": {"description": "Available offline status"}, "streamsFromInternet": "از اینترنت پخش می‌شود", "@streamsFromInternet": {"description": "Streams from internet status"}, "downloadForOfflineUse": "برای استفاده آفلاین دانلود کنید", "@downloadForOfflineUse": {"description": "Download for offline use tooltip"}, "notificationPermission": "مجوز اعلان", "@notificationPermission": {"description": "Notification permission dialog title"}, "notificationPermissionRequired": "مجوز اعلان برای ارسال یادآوری‌های تنفس مورد نیاز است. می‌توانید آن را در تنظیمات تلفن خود فعال کنید یا دوباره درخواست مجوز دهید.", "@notificationPermissionRequired": {"description": "Notification permission required message"}, "remindersNotExact": "یادآوری‌ها ممکن است به دلیل محدودیت‌های سیستم دقیق نباشند. آنها همچنان کار خواهند کرد، اما زمان‌بندی ممکن است کمی متفاوت باشد.", "@remindersNotExact": {"description": "Reminders may not be exact message"}}